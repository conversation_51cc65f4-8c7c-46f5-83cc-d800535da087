// 时钟组件
class ClockWidget {
  constructor() {
    this.apiKey = 'f7105a35d70abd283a0dd10f87cf2516';
    this.city = null; // 不设定默认城市，等待获取
    this.weatherData = null;
    this.locationObtained = false; // 标记是否已获取到位置
    this.storageKey = 'clock-widget-city'; // localStorage键名
    this.init();
  }

  // 从localStorage加载城市信息
  loadSavedCity() {
    try {
      const savedCity = localStorage.getItem(this.storageKey);
      if (savedCity) {
        this.city = savedCity;
        this.locationObtained = true;
        console.log('📱 加载保存的城市:', this.city);
        
        // 更新城市显示
        const cityElement = document.querySelector('.clock-city');
        if (cityElement) {
          cityElement.textContent = this.city;
        }
        
        // 获取天气数据
        this.getWeatherData();
        return true;
      }
    } catch (error) {
      console.warn('⚠️ 加载保存的城市失败:', error);
    }
    return false;
  }

  // 保存城市信息到localStorage
  saveCity(city) {
    try {
      localStorage.setItem(this.storageKey, city);
      console.log('💾 保存城市信息:', city);
    } catch (error) {
      console.warn('⚠️ 保存城市信息失败:', error);
    }
  }

  // 检查是否启用自动主题切换
  isAutoThemeEnabled() {
    try {
      const setting = localStorage.getItem(this.autoThemeStorageKey);
      return setting !== 'false'; // 默认启用，除非明确设置为false
    } catch (error) {
      return true; // 默认启用
    }
  }

  // 设置自动主题切换
  setAutoTheme(enabled) {
    try {
      localStorage.setItem(this.autoThemeStorageKey, enabled.toString());
      console.log('🎨 自动主题切换设置:', enabled ? '启用' : '禁用');
    } catch (error) {
      console.warn('⚠️ 保存自动主题设置失败:', error);
    }
  }

  // 自动主题切换逻辑
  autoSwitchTheme() {
    if (!this.isAutoThemeEnabled()) {
      return;
    }

    const now = new Date();
    const hour = now.getHours();
    
    // 避免频繁检查，只在小时变化时检查
    if (this.lastThemeCheck === hour) {
      return;
    }
    
    this.lastThemeCheck = hour;
    
    // 获取当前主题状态
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode') ||
                      document.body.getAttribute('data-theme') === 'dark';
    
    // 定义主题切换时间：6:00-18:00为浅色主题，18:00-6:00为深色主题
    const shouldBeDark = hour < 6 || hour >= 18;
    
    // 如果当前主题与应该的主题不匹配，则切换
    if (shouldBeDark && !isDarkMode) {
      console.log('🌙 自动切换到深色主题 (当前时间:', hour + ':00)');
      this.switchToDarkMode();
    } else if (!shouldBeDark && isDarkMode) {
      console.log('☀️ 自动切换到浅色主题 (当前时间:', hour + ':00)');
      this.switchToLightMode();
    }
  }

  // 切换到深色主题
  switchToDarkMode() {
    try {
      // 尝试调用现有的主题切换函数
      if (typeof sco !== 'undefined' && sco.switchDarkMode) {
        // 检查当前是否为浅色主题，如果是则切换
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode');
        if (!isDark) {
          sco.switchDarkMode();
        }
      } else {
        // 备用方案：直接设置主题属性
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
        localStorage.setItem('theme', 'dark');
      }
    } catch (error) {
      console.warn('⚠️ 切换到深色主题失败:', error);
    }
  }

  // 切换到浅色主题
  switchToLightMode() {
    try {
      // 尝试调用现有的主题切换函数
      if (typeof sco !== 'undefined' && sco.switchDarkMode) {
        // 检查当前是否为深色主题，如果是则切换
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode');
        if (isDark) {
          sco.switchDarkMode();
        }
      } else {
        // 备用方案：直接设置主题属性
        document.documentElement.setAttribute('data-theme', 'light');
        document.body.classList.remove('dark-mode');
        localStorage.setItem('theme', 'light');
      }
    } catch (error) {
      console.warn('⚠️ 切换到浅色主题失败:', error);
    }
  }

  // 使用免费的逆地理编码服务
  async reverseGeocode(latitude, longitude) {
    try {
      console.log('🔄 正在进行逆地理编码...');
      
      // 使用OpenStreetMap的Nominatim服务（免费且不受跨域限制）
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&accept-language=zh-CN`,
        {
          headers: {
            'User-Agent': 'ClockWidget/1.0'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        console.log('🌍 逆地理编码响应:', data);
        
        // 提取城市信息
        let city = null;
        if (data.address) {
          // 优先级：city > town > county > state
          city = data.address.city || 
                 data.address.town || 
                 data.address.county || 
                 data.address.state;
          
          // 如果获取到城市信息
          if (city) {
            // 确保城市名称以"市"结尾（适配高德API格式）
            if (!city.endsWith('市') && !city.endsWith('县') && !city.endsWith('区')) {
              city += '市';
            }
            
            this.city = city;
            this.locationObtained = true;
            console.log('✅ 成功获取城市:', this.city);
            
            // 保存城市信息
            this.saveCity(this.city);
            
            // 更新城市显示
            const cityElement = document.querySelector('.clock-city');
            if (cityElement) {
              cityElement.textContent = this.city;
            }
            
            // 获取天气数据
            this.getWeatherData();
            return;
          }
        }
        
        console.warn('⚠️ 无法从逆地理编码结果中提取城市信息');
      } else {
        console.warn('⚠️ 逆地理编码请求失败:', response.status);
      }
    } catch (error) {
      console.error('❌ 逆地理编码失败:', error);
    }
    
    // 如果逆地理编码失败，不设置城市，等待超时使用备用方案
    console.log('📍 逆地理编码失败，等待超时使用备用城市');
  }

  // 设置备用城市的方法
  setFallbackCity() {
    this.city = '北京市'; // 使用北京作为备用城市
    this.locationObtained = true;
    console.log('🏙️ 设置备用城市:', this.city);
    
    // 保存备用城市信息
    this.saveCity(this.city);
    
    // 更新城市显示
    const cityElement = document.querySelector('.clock-city');
    if (cityElement) {
      cityElement.textContent = this.city;
    }
    
    // 获取天气数据
    this.getWeatherData();
  }

  init() {
    this.createClockElement();
    this.updateTime();
    this.setupEventListeners();
    
    // 先尝试加载保存的城市信息
    const hasSavedCity = this.loadSavedCity();
    
    // 如果没有保存的城市，则获取用户位置
    if (!hasSavedCity) {
      this.getUserLocation();
    }
    
    // 无论如何都要尝试获取最新位置（后台更新）
    setTimeout(() => {
      this.getUserLocation();
    }, 2000); // 2秒后开始后台位置更新
    
    // 每秒更新时间
    setInterval(() => this.updateTime(), 1000);
    
    // 每30分钟更新天气（仅在已获取到城市信息时）
    setInterval(() => {
      if (this.locationObtained && this.city) {
        this.getWeatherData();
      }
    }, 30 * 60 * 1000);
  }

  // 设置事件监听器
  setupEventListeners() {
    // 延迟设置事件监听器，确保DOM元素已创建
    setTimeout(() => {
      const autoThemeSwitch = document.getElementById('auto-theme-switch');
      if (autoThemeSwitch) {
        autoThemeSwitch.addEventListener('change', (e) => {
          const enabled = e.target.checked;
          this.setAutoTheme(enabled);
          
          if (enabled) {
            console.log('✅ 自动主题切换已启用');
            // 立即检查一次主题
            this.lastThemeCheck = null;
            this.autoSwitchTheme();
          } else {
            console.log('❌ 自动主题切换已禁用');
          }
        });
      }
    }, 100);
  }

  createClockElement() {
    const clockHTML = `
      <div class="clock-widget">
        <div class="clock-header">
          <span class="clock-date"></span>
          <span class="clock-weather">
            <i class="fas fa-cloud-sun"></i>
            <span>多云 37°C</span>
            <i class="fas fa-tint"></i>
            <span>39%</span>
          </span>
        </div>
        <div class="clock-time">16:46:15</div>
        <div class="clock-location">
          <i class="fas fa-map-marker-alt"></i>
          <span class="clock-city">获取中...</span>
        </div>
        <div class="clock-controls">
          <div class="auto-theme-toggle">
            <i class="fas fa-palette"></i>
            <span>自动主题</span>
            <label class="switch">
              <input type="checkbox" id="auto-theme-switch" ${this.isAutoThemeEnabled() ? 'checked' : ''}>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
    `;
    

  }

  updateTime() {
    const now = new Date();
    const timeElement = document.querySelector('.clock-time');
    const dateElement = document.querySelector('.clock-date');
    
    if (timeElement && dateElement) {
      // 格式化时间
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      // 格式化日期
      const dateString = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'short'
      });
      
      timeElement.textContent = timeString;
      dateElement.textContent = dateString;
    }
    
    // 检查并执行自动主题切换
    this.autoSwitchTheme();
  }

  loadAMap(callback) {
    if (window.AMap) {
      callback();
      return;
    }
    const script = document.createElement('script');
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${this.apiKey}`;
    script.onload = callback;
    document.head.appendChild(script);
  }

  async getUserLocation() {
    console.log('🌍 开始获取用户位置...');
    this.tryBrowserGeolocation();
    setTimeout(() => {
      if (!this.locationObtained) {
        console.log('⏰ 位置获取超时，使用备用城市');
        this.setFallbackCity();
      }
    }, 15000);
  }

  // Delete tryGetCityByIP method

  async tryGetCityByIP() {
    this.loadAMap(() => {
      AMap.plugin('AMap.CitySearch', () => {
        const citySearch = new AMap.CitySearch();
        citySearch.getLocalCity((status, result) => {
          if (status === 'complete' && result.info === 'OK') {
            this.city = result.city;
            this.locationObtained = true;
            console.log('✅ 成功获取城市:', this.city);
            this.saveCity(this.city);
            const cityElement = document.querySelector('.clock-city');
            if (cityElement) {
              cityElement.textContent = this.city;
            }
            this.getWeatherData();
          } else {
            console.warn('⚠️ IP定位失败:', result);
            this.setFallbackCity();
          }
        });
      });
    });
  }

  // Remove reverseGeocode and tryBrowserGeolocation
  async tryBrowserGeolocation() {
    if ('geolocation' in navigator) {
      console.log('🌍 尝试使用浏览器地理位置API...');
      
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { latitude, longitude } = position.coords;
            console.log('📍 获取到坐标:', latitude, longitude);
            
            // 使用免费的逆地理编码服务（不受跨域限制）
            await this.reverseGeocode(latitude, longitude);
          } catch (error) {
            console.error('❌ 地理位置处理失败:', error);
          }
        },
        (error) => {
          let errorMsg = '';
          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMsg = '用户拒绝了地理位置请求';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMsg = '位置信息不可用';
              break;
            case error.TIMEOUT:
              errorMsg = '获取位置信息超时';
              break;
            default:
              errorMsg = '获取位置信息时发生未知错误';
              break;
          }
          console.warn('⚠️ 浏览器地理位置获取失败:', errorMsg);
          // 不在这里设置城市，等待超时后使用备用方案
        },
        {
          timeout: 10000,
          enableHighAccuracy: false,
          maximumAge: 300000 // 5分钟内的缓存位置可接受
        }
      );
    } else {
      console.log('❌ 浏览器不支持地理位置API');
      // 不在这里设置城市，等待超时后使用备用方案
    }
  }

  async getWeatherData() {
    if (!this.city) {
      console.warn('⚠️ 尚未获取到城市信息，跳过天气数据获取');
      return;
    }
    this.loadAMap(() => {
      AMap.plugin('AMap.Weather', () => {
        const weather = new AMap.Weather();
        weather.getLive(this.city, (err, data) => {
          if (!err) {
            this.weatherData = data;
            console.log('✅ 成功获取天气数据:', this.weatherData);
            this.updateWeatherDisplay();
          } else {
            console.warn('⚠️ 天气获取失败:', err);
            this.updateWeatherDisplay(true);
          }
        });
      });
    });
  }

  updateWeatherDisplay(error = false) {
    const weatherElement = document.querySelector('.clock-weather');
    
    if (weatherElement) {
      if (error || !this.weatherData) {
        weatherElement.innerHTML = `
          <i class="fas fa-cloud"></i>
          <span>多云 37°C</span>
          <i class="fas fa-tint"></i>
          <span>39%</span>
        `;
      } else {
        const weather = this.weatherData;
        const weatherIcon = this.getWeatherIcon(weather.weather);
        
        weatherElement.innerHTML = `
          <i class="${weatherIcon}"></i>
          <span>${weather.weather} ${weather.temperature}°C</span>
          <i class="fas fa-tint"></i>
          <span>${weather.humidity}%</span>
        `;
      }
    }
  }

  getWeatherIcon(weather) {
    const iconMap = {
      '晴': 'fas fa-sun',
      '多云': 'fas fa-cloud-sun',
      '阴': 'fas fa-cloud',
      '雨': 'fas fa-cloud-rain',
      '雪': 'fas fa-snowflake',
      '雾': 'fas fa-smog',
      '霾': 'fas fa-smog'
    };
    
    for (const key in iconMap) {
      if (weather.includes(key)) {
        return iconMap[key];
      }
    }
    
    return 'fas fa-cloud';
  }
}

// 页面加载完成后初始化时钟组件
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    new ClockWidget();
  }, 1000);
});

if (typeof pjax !== 'undefined') {
  document.addEventListener('pjax:complete', () => {
    setTimeout(() => {
      new ClockWidget();
    }, 1000);
  });
}