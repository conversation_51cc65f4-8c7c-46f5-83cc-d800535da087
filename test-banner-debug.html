<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟调试测试</title>
    <link rel="stylesheet" href="http://localhost:4000/css/custom.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #banners {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>横幅时钟调试测试</h1>
        <p>这个页面用于调试横幅时钟组件的问题。</p>
        <p>预期功能：</p>
        <ul>
            <li>✅ 显示实时时间（时:分:秒）</li>
            <li>✅ 显示当前日期（年月日 星期）</li>
            <li>🌤️ 显示天气信息（天气状况、温度、湿度）</li>
            <li>📍 显示位置信息（自动获取或默认城市）</li>
        </ul>
    </div>
    
    <div id="banners">
        <!-- 横幅时钟将通过JavaScript动态添加到这里 -->
    </div>
    
    <div class="debug-info">
        <h3>调试信息</h3>
        <div id="debug-output"></div>
    </div>
    
    <script src="http://localhost:4000/js/banner-clock.js"></script>
    <script>
        // 调试输出函数
        function debugLog(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(`[DEBUG] ${message}`);
        }
        
        // 创建横幅时钟
        function createBannerClock() {
            debugLog('开始创建横幅时钟...');
            
            const banners = document.getElementById('banners');
            if (!banners) {
                debugLog('❌ 找不到 banners 元素');
                return;
            }
            
            if (document.getElementById('banner-clock')) {
                debugLog('⚠️ banner-clock 元素已存在');
                return;
            }
            
            const bannerClock = document.createElement('div');
            bannerClock.id = 'banner-clock';
            bannerClock.innerHTML = `
                <div class="banner-clock-time">加载中...</div>
                <div class="banner-clock-date">加载中...</div>
                <div class="banner-clock-weather">
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-cloud-sun"></i>
                        <span>多云 25°C</span>
                    </div>
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-tint"></i>
                        <span>65%</span>
                    </div>
                </div>
                <div class="banner-clock-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>获取位置中...</span>
                </div>
            `;
            banners.appendChild(bannerClock);
            debugLog('✅ 横幅时钟 DOM 元素已创建');
            
            // 检查 BannerClockWidget 是否可用
            setTimeout(() => {
                if (typeof BannerClockWidget !== 'undefined') {
                    debugLog('✅ BannerClockWidget 类已加载，开始初始化...');
                    try {
                        new BannerClockWidget();
                        debugLog('✅ BannerClockWidget 初始化成功');
                    } catch (error) {
                        debugLog(`❌ BannerClockWidget 初始化失败: ${error.message}`);
                    }
                } else {
                    debugLog('❌ BannerClockWidget 类未定义，使用备用时钟');
                    // 备用简单时钟
                    function updateBannerClock() {
                        const timeEl = bannerClock.querySelector('.banner-clock-time');
                        const dateEl = bannerClock.querySelector('.banner-clock-date');
                        if (timeEl && dateEl) {
                            const now = new Date();
                            timeEl.textContent = now.toLocaleTimeString('zh-CN', {
                                hour12: false,
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                            dateEl.textContent = now.toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                weekday: 'long'
                            });
                        }
                    }
                    updateBannerClock();
                    setInterval(updateBannerClock, 1000);
                    debugLog('✅ 备用时钟已启动');
                }
            }, 100);
        }
        
        // 页面加载完成后创建时钟
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面 DOM 加载完成');
            createBannerClock();
        });
    </script>
</body>
</html>
