// 横幅时钟组件
class BannerClockWidget {
  constructor() {
    this.apiKey = 'f7105a35d70abd283a0dd10f87cf2516';
    this.city = null;
    this.weatherData = null;
    this.locationObtained = false;
    this.storageKey = 'banner-clock-widget-city';
    this.init();
  }

  // 从localStorage加载城市信息
  loadSavedCity() {
    try {
      const savedCity = localStorage.getItem(this.storageKey);
      if (savedCity) {
        this.city = savedCity;
        this.locationObtained = true;
        console.log('🏙️ 横幅时钟加载保存的城市:', this.city);
        
        // 更新城市显示
        const cityElement = document.querySelector('#banner-clock .clock-city');
        if (cityElement) {
          cityElement.textContent = this.city;
        }
        
        // 获取天气数据
        this.getWeatherData();
        return true;
      }
    } catch (error) {
      console.warn('⚠️ 横幅时钟加载保存的城市失败:', error);
    }
    return false;
  }

  // 保存城市信息到localStorage
  saveCity(city) {
    try {
      localStorage.setItem(this.storageKey, city);
      console.log('💾 横幅时钟保存城市信息:', city);
    } catch (error) {
      console.warn('⚠️ 横幅时钟保存城市信息失败:', error);
    }
  }

  // 初始化
  init() {
    console.log('🕐 横幅时钟组件初始化...');
    
    // 先尝试加载保存的城市
    if (!this.loadSavedCity()) {
      // 如果没有保存的城市，尝试获取位置
      this.getCurrentLocation();
    }
    
    // 启动时钟更新
    this.updateTime();
    this.startClock();
    
    // 定期更新天气（每30分钟）
    setInterval(() => {
      if (this.city) {
        this.getWeatherData();
      }
    }, 30 * 60 * 1000);
  }

  // 获取当前位置
  getCurrentLocation() {
    if (navigator.geolocation) {
      console.log('📍 横幅时钟正在获取位置...');
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          console.log('✅ 横幅时钟位置获取成功:', position.coords);
          this.reverseGeocode(position.coords.latitude, position.coords.longitude);
        },
        (error) => {
          console.warn('⚠️ 横幅时钟位置获取失败:', error.message);
          this.setDefaultCity();
        },
        {
          timeout: 10000,
          enableHighAccuracy: false,
          maximumAge: 300000 // 5分钟缓存
        }
      );
    } else {
      console.warn('⚠️ 浏览器不支持地理位置');
      this.setDefaultCity();
    }
  }

  // 设置默认城市
  setDefaultCity() {
    this.city = '北京市';
    this.locationObtained = true;
    console.log('🏙️ 横幅时钟使用默认城市:', this.city);
    
    const cityElement = document.querySelector('#banner-clock .clock-city');
    if (cityElement) {
      cityElement.textContent = this.city;
    }
    
    this.saveCity(this.city);
    this.getWeatherData();
  }

  // 逆地理编码
  async reverseGeocode(latitude, longitude) {
    try {
      console.log('🔄 横幅时钟正在进行逆地理编码...');
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&accept-language=zh-CN`,
        {
          headers: {
            'User-Agent': 'BannerClockWidget/1.0'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        console.log('🌍 横幅时钟逆地理编码响应:', data);
        
        let city = null;
        if (data.address) {
          city = data.address.city || 
                 data.address.town || 
                 data.address.county || 
                 data.address.state;
          
          if (city) {
            if (!city.endsWith('市') && !city.endsWith('县') && !city.endsWith('区')) {
              city += '市';
            }
            
            this.city = city;
            this.locationObtained = true;
            console.log('✅ 横幅时钟成功获取城市:', this.city);

            this.saveCity(this.city);

            const cityElement = document.querySelector('#banner-clock .clock-city');
            if (cityElement) {
              cityElement.textContent = this.city;
            }
            
            this.getWeatherData();
            return;
          }
        }
        
        console.warn('⚠️ 横幅时钟无法从逆地理编码结果中提取城市信息');
      } else {
        console.warn('⚠️ 横幅时钟逆地理编码请求失败:', response.status);
      }
    } catch (error) {
      console.warn('⚠️ 横幅时钟逆地理编码出错:', error);
    }
    
    // 如果逆地理编码失败，使用默认城市
    this.setDefaultCity();
  }

  // 获取天气数据
  async getWeatherData() {
    if (!this.city) {
      console.warn('⚠️ 横幅时钟城市信息未设置，无法获取天气');
      return;
    }

    try {
      console.log('🌤️ 横幅时钟正在获取天气数据:', this.city);
      
      const response = await fetch(
        `https://restapi.amap.com/v3/weather/weatherInfo?key=${this.apiKey}&city=${encodeURIComponent(this.city)}&extensions=base`
      );
      
      if (response.ok) {
        const data = await response.json();
        console.log('🌤️ 横幅时钟天气API响应:', data);
        
        if (data.status === '1' && data.lives && data.lives.length > 0) {
          this.weatherData = data.lives[0];
          this.updateWeatherDisplay();
        } else {
          console.warn('⚠️ 横幅时钟天气API返回错误:', data.info || '未知错误');
          this.setDefaultWeather();
        }
      } else {
        console.warn('⚠️ 横幅时钟天气请求失败:', response.status);
        this.setDefaultWeather();
      }
    } catch (error) {
      console.warn('⚠️ 横幅时钟获取天气数据出错:', error);
      this.setDefaultWeather();
    }
  }

  // 设置默认天气
  setDefaultWeather() {
    this.weatherData = {
      weather: '多云',
      temperature: '25',
      humidity: '65'
    };
    this.updateWeatherDisplay();
  }

  // 更新天气显示
  updateWeatherDisplay() {
    if (!this.weatherData) return;

    const weatherItems = document.querySelectorAll('#banner-clock .banner-clock-weather-item span');
    if (weatherItems.length >= 2) {
      // 更新温度
      weatherItems[0].textContent = `${this.weatherData.weather} ${this.weatherData.temperature}°C`;
      // 更新湿度
      weatherItems[1].textContent = `${this.weatherData.humidity}%`;
    }

    // 根据天气更新图标
    this.updateWeatherIcon();
  }

  // 更新天气图标
  updateWeatherIcon() {
    if (!this.weatherData) return;

    const weatherIcon = document.querySelector('#banner-clock .banner-clock-weather-item i');
    if (!weatherIcon) return;

    const weather = this.weatherData.weather;
    let iconClass = 'fas fa-cloud-sun'; // 默认图标

    if (weather.includes('晴')) {
      iconClass = 'fas fa-sun';
    } else if (weather.includes('雨')) {
      iconClass = 'fas fa-cloud-rain';
    } else if (weather.includes('雪')) {
      iconClass = 'fas fa-snowflake';
    } else if (weather.includes('阴')) {
      iconClass = 'fas fa-cloud';
    } else if (weather.includes('雾') || weather.includes('霾')) {
      iconClass = 'fas fa-smog';
    }

    weatherIcon.className = iconClass;
  }

  // 更新时间
  updateTime() {
    const now = new Date();
    
    // 更新时间显示
    const timeElement = document.querySelector('#banner-clock .banner-clock-time');
    if (timeElement) {
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      timeElement.textContent = timeString;
    }

    // 更新日期显示
    const dateElement = document.querySelector('#banner-clock .banner-clock-date');
    if (dateElement) {
      const dateString = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });
      dateElement.textContent = dateString;
    }
  }

  // 启动时钟
  startClock() {
    // 每秒更新时间
    setInterval(() => {
      this.updateTime();
    }, 1000);
    
    console.log('⏰ 横幅时钟已启动');
  }
}

// 确保在全局作用域中可用
window.BannerClockWidget = BannerClockWidget;